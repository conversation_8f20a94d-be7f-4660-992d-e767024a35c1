import { Screen } from "@/app-components/layout/screen";
import {
  Users,
  UserPlus,
  MapPin,
  FileText,
  Calendar,
  Clock,
  AlertCircle,
  Activity,
  Target,
  BarChart3,
  Bell,
} from "lucide-react";
import { useAuthHook } from "@/utils/useAuthHook";
import { Link } from "react-router-dom";
import { useState, useMemo } from "react";
import AddProspects from "./Prospects/addlead";
import { useGetDashboardQuery } from "@/redux/slices/dashboard";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useGetTodoQuery } from "@/redux/slices/todoApiSlice";

// Helper function to get time-based greeting
const getTimeBasedGreeting = () => {
  const hour = new Date().getHours();
  if (hour >= 5 && hour < 12) return "Good Morning";
  if (hour >= 12 && hour < 17) return "Good Afternoon";
  if (hour >= 17 && hour < 22) return "Good Evening";
  return "Good Night";
};

// Helper function to get user role display
const getUserRoleDisplay = (user_details: any) => {
  const department = user_details?.department || "";
  const team = user_details?.team || "";
  const office = user_details?.office || "";

  return {
    department,
    team,
    office,
    displayRole: department || team || "Employee"
  };
};

const Home = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // User authentication and information
  const { user_details } = useAuthHook();
  const employee_no = user_details?.employee_no || "";
  const employee_name = user_details?.fullnames || "";
  const userRole = getUserRoleDisplay(user_details);

  // API calls for dashboard data
  const { data: dashboardData, isLoading, isFetching } = useGetDashboardQuery({
    EMPLOYEE_NO: employee_no,
  });

  // Get user's services/todos filtered by employee_no
  const { data: servicesData } = useGetTodoQuery({
    assigned_to: employee_no,
    page: 1,
    page_size: 10,
  });

  // Quick actions based on user permissions and role
  const quickActions = useMemo(() => {
    const actions = [
      {
        title: "View Customers",
        description: "Browse all customers",
        icon: <Users className="w-5 h-5 text-gray-600 dark:text-gray-400" />,
        link: "/Customers",
      },
      {
        title: "View Prospects",
        description: "Manage prospects and leads",
        icon: <UserPlus className="w-5 h-5 text-gray-600 dark:text-gray-400" />,
        link: "/prospects",
      },
      {
        title: "Book Site Visit",
        description: "Schedule customer visits",
        icon: <MapPin className="w-5 h-5 text-gray-600 dark:text-gray-400" />,
        link: "/book-visit",
      },
      {
        title: "View Reports",
        description: "Access sales reports",
        icon: <BarChart3 className="w-5 h-5 text-gray-600 dark:text-gray-400" />,
        link: "/marketerreport",
      },
      {
        title: "My Bookings",
        description: "View your bookings",
        icon: <Calendar className="w-5 h-5 text-gray-600 dark:text-gray-400" />,
        link: "/mybookings",
      },
      {
        title: "Projects",
        description: "Browse available projects",
        icon: <Target className="w-5 h-5 text-gray-600 dark:text-gray-400" />,
        link: "/projects",
      },
    ];

    return actions;
  }, []);

  // Dashboard stats from API
  const dashboardStats = useMemo(() => {
    const stats = [
      {
        title: "Reminders",
        count: dashboardData?.reminders?.data?.length || 0,
        icon: <Clock className="w-5 h-5 text-gray-500 dark:text-gray-400" />,
        link: "/reminders",
      },
      {
        title: "Notifications",
        count: dashboardData?.notifications?.data?.length || 0,
        icon: <Bell className="w-5 h-5 text-gray-500 dark:text-gray-400" />,
        link: "/notifications",
      },
      {
        title: "Notes",
        count: dashboardData?.notes?.data?.length || 0,
        icon: <FileText className="w-5 h-5 text-gray-500 dark:text-gray-400" />,
        link: "/notes",
      },
      {
        title: "Tickets",
        count: dashboardData?.tickets?.data?.length || 0,
        icon: <AlertCircle className="w-5 h-5 text-gray-500 dark:text-gray-400" />,
        link: "/complaints",
      },
    ];

    return stats;
  }, [dashboardData]);

  // Services/Tasks data for the user
  const userServices = useMemo(() => {
    if (!servicesData?.results) return [];

    return servicesData.results.slice(0, 5).map((service: any) => ({
      id: service.id,
      title: service.title || service.task || "Task",
      description: service.description || service.details || "",
      status: service.status || "PENDING",
      created_at: service.created_at,
      due_date: service.due_date,
    }));
  }, [servicesData]);

  // Show loader when initially loading or when tab is changing
  const showLoader = isLoading || isFetching;

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 relative transition-colors duration-200">
        {showLoader && (
          <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70 dark:bg-gray-900 dark:bg-opacity-70">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        )}

        <div className="p-6 space-y-6">
          {/* Welcome Section */}
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {getTimeBasedGreeting()}, {employee_name?.split(" ")[0] || "User"}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-3">
                  Welcome back to Optiven CRM Dashboard
                </p>
                <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                    <span>{userRole.office}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                    <span>{userRole.department}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                    <span>{userRole.team}</span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">Employee ID</p>
                <p className="font-semibold text-gray-900 dark:text-gray-100">{employee_no}</p>
                <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>
          </div>

          {/* Quick Actions Grid */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  to={action.link}
                  className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200"
                >
                  <div className="flex items-center space-x-3">
                    {action.icon}
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">{action.title}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{action.description}</p>
                    </div>
                  </div>
                </Link>
              ))}

              {/* Add Prospect Button */}
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200"
              >
                <div className="flex items-center space-x-3">
                  <UserPlus className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">Add Prospect</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Create new lead</p>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* Dashboard Stats */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Dashboard Overview
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {dashboardStats.map((stat, index) => (
                <Link
                  key={index}
                  to={stat.link}
                  className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-6 rounded-lg hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                        {stat.title}
                      </h3>
                      <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mt-2">
                        {stat.count}
                      </p>
                    </div>
                    {stat.icon}
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Services/Tasks Section */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              My Services & Tasks
            </h2>
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
              {userServices.length > 0 ? (
                <div className="space-y-3">
                  {userServices.map((service: any) => (
                    <div
                      key={service.id}
                      className="flex items-center justify-between p-4 border border-gray-100 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          {service.title}
                        </h4>
                        {service.description && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {service.description}
                          </p>
                        )}
                        {service.due_date && (
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                            Due: {new Date(service.due_date).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                      <div className="ml-4">
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-medium border ${
                            service.status === "COMPLETED"
                              ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                              : service.status === "IN_PROGRESS"
                              ? "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800"
                              : "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600"
                          }`}
                        >
                          {service.status}
                        </span>
                      </div>
                    </div>
                  ))}
                  <div className="text-center pt-4 border-t border-gray-100 dark:border-gray-700">
                    <Link
                      to="/admin/services"
                      className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 text-sm font-medium transition-colors"
                    >
                      View All Services →
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Activity className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    No services assigned to you at the moment
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Recent Activity
            </h2>
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
              <div className="space-y-3">
                {/* Recent Reminders */}
                {dashboardData?.reminders?.data?.slice(0, 3).map((reminder: any, index: number) => (
                  <div
                    key={`reminder-${index}`}
                    className="flex items-start space-x-3 p-3 border border-gray-100 dark:border-gray-700 rounded-lg"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <Clock className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {reminder.text || "Reminder"}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Due: {reminder.date || "Soon"}
                      </p>
                    </div>
                  </div>
                ))}

                {/* Recent Tickets */}
                {dashboardData?.tickets?.data?.slice(0, 2).map((ticket: any, index: number) => (
                  <div
                    key={`ticket-${index}`}
                    className="flex items-start space-x-3 p-3 border border-gray-100 dark:border-gray-700 rounded-lg"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <AlertCircle className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Ticket: {ticket.name || "Support Request"}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Status: {ticket.status || "Open"} • {ticket.date || "Recent"}
                      </p>
                    </div>
                  </div>
                ))}

                {/* No Activity Message */}
                {(!dashboardData?.reminders?.data?.length && !dashboardData?.tickets?.data?.length) && (
                  <div className="text-center py-12">
                    <Activity className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      No recent activity to display
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

        </div>

        {/* Add Prospect Modal */}
        {isAddModalOpen && (
          <AddProspects
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
          />
        )}
      </div>
    </Screen>
  );
};
export default Home;