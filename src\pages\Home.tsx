import { Screen } from "@/app-components/layout/screen";
import {
  Users,
  UserPlus,
  MapPin,
  FileText,
  Calendar,
  Clock,
  AlertCircle,
  Activity,
  Target,
  BarChart3,
  Bell,
} from "lucide-react";
import { useAuthHook } from "@/utils/useAuthHook";
import { Link } from "react-router-dom";
import { useState, useMemo } from "react";
import AddProspects from "./Prospects/addlead";
import { useGetDashboardQuery } from "@/redux/slices/dashboard";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useGetTodoQuery } from "@/redux/slices/todoApiSlice";

// Helper function to get time-based greeting
const getTimeBasedGreeting = () => {
  const hour = new Date().getHours();
  if (hour >= 5 && hour < 12) return "Good Morning";
  if (hour >= 12 && hour < 17) return "Good Afternoon";
  if (hour >= 17 && hour < 22) return "Good Evening";
  return "Good Night";
};

// Helper function to get user role display
const getUserRoleDisplay = (user_details: any) => {
  const department = user_details?.department || "";
  const team = user_details?.team || "";
  const office = user_details?.office || "";

  return {
    department,
    team,
    office,
    displayRole: department || team || "Employee"
  };
};

const Home = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // User authentication and information
  const { user_details } = useAuthHook();
  const employee_no = user_details?.employee_no || "";
  const employee_name = user_details?.fullnames || "";
  const userRole = getUserRoleDisplay(user_details);

  // API calls for dashboard data
  const { data: dashboardData, isLoading, isFetching } = useGetDashboardQuery({
    EMPLOYEE_NO: employee_no,
  });

  // Get user's services/todos filtered by employee_no
  const { data: servicesData } = useGetTodoQuery({
    assigned_to: employee_no,
    page: 1,
    page_size: 10,
  });

  // Quick actions based on user permissions and role
  const quickActions = useMemo(() => {
    const actions = [
      {
        title: "View Customers",
        description: "Browse all customers",
        icon: <Users className="w-6 h-6" />,
        link: "/Customers",
        color: "bg-blue-500 hover:bg-blue-600",
      },
      {
        title: "View Prospects",
        description: "Manage prospects and leads",
        icon: <UserPlus className="w-6 h-6" />,
        link: "/prospects",
        color: "bg-green-500 hover:bg-green-600",
      },
      {
        title: "Book Site Visit",
        description: "Schedule customer visits",
        icon: <MapPin className="w-6 h-6" />,
        link: "/book-visit",
        color: "bg-purple-500 hover:bg-purple-600",
      },
      {
        title: "View Reports",
        description: "Access sales reports",
        icon: <BarChart3 className="w-6 h-6" />,
        link: "/marketerreport",
        color: "bg-orange-500 hover:bg-orange-600",
      },
      {
        title: "My Bookings",
        description: "View your bookings",
        icon: <Calendar className="w-6 h-6" />,
        link: "/mybookings",
        color: "bg-indigo-500 hover:bg-indigo-600",
      },
      {
        title: "Projects",
        description: "Browse available projects",
        icon: <Target className="w-6 h-6" />,
        link: "/projects",
        color: "bg-teal-500 hover:bg-teal-600",
      },
    ];

    return actions;
  }, []);

  // Dashboard stats from API
  const dashboardStats = useMemo(() => {
    const stats = [
      {
        title: "Reminders",
        count: dashboardData?.reminders?.data?.length || 0,
        icon: <Clock className="w-6 h-6 text-blue-600" />,
        color: "bg-blue-50 border-l-4 border-blue-500",
        link: "/reminders",
      },
      {
        title: "Notifications",
        count: dashboardData?.notifications?.data?.length || 0,
        icon: <Bell className="w-6 h-6 text-yellow-600" />,
        color: "bg-yellow-50 border-l-4 border-yellow-500",
        link: "/notifications",
      },
      {
        title: "Notes",
        count: dashboardData?.notes?.data?.length || 0,
        icon: <FileText className="w-6 h-6 text-green-600" />,
        color: "bg-green-50 border-l-4 border-green-500",
        link: "/notes",
      },
      {
        title: "Tickets",
        count: dashboardData?.tickets?.data?.length || 0,
        icon: <AlertCircle className="w-6 h-6 text-red-600" />,
        color: "bg-red-50 border-l-4 border-red-500",
        link: "/complaints",
      },
    ];

    return stats;
  }, [dashboardData]);

  // Services/Tasks data for the user
  const userServices = useMemo(() => {
    if (!servicesData?.results) return [];

    return servicesData.results.slice(0, 5).map((service: any) => ({
      id: service.id,
      title: service.title || service.task || "Task",
      description: service.description || service.details || "",
      status: service.status || "PENDING",
      created_at: service.created_at,
      due_date: service.due_date,
    }));
  }, [servicesData]);

  // Show loader when initially loading or when tab is changing
  const showLoader = isLoading || isFetching;

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 relative transition-colors duration-200">
        {showLoader && (
          <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70 dark:bg-gray-900 dark:bg-opacity-70">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        )}

        <div className="p-6 space-y-6">
          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold mb-2">
                  {getTimeBasedGreeting()}, {employee_name?.split(" ")[0] || "User"}! 👋
                </h1>
                <p className="text-blue-100 mb-1">
                  Welcome back to Optiven CRM Dashboard
                </p>
                <div className="flex items-center space-x-4 text-sm text-blue-100">
                  <span>📍 {userRole.office}</span>
                  <span>🏢 {userRole.department}</span>
                  <span>👥 {userRole.team}</span>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-blue-100">Employee ID</p>
                <p className="font-semibold">{employee_no}</p>
                <p className="text-xs text-blue-200 mt-1">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>
          </div>

          {/* Quick Actions Grid */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  to={action.link}
                  className={`${action.color} text-white p-4 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:scale-105`}
                >
                  <div className="flex items-center space-x-3">
                    {action.icon}
                    <div>
                      <h3 className="font-semibold">{action.title}</h3>
                      <p className="text-sm opacity-90">{action.description}</p>
                    </div>
                  </div>
                </Link>
              ))}

              {/* Add Prospect Button */}
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:scale-105"
              >
                <div className="flex items-center space-x-3">
                  <UserPlus className="w-6 h-6" />
                  <div>
                    <h3 className="font-semibold">Add Prospect</h3>
                    <p className="text-sm opacity-90">Create new lead</p>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* Dashboard Stats */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Dashboard Overview
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {dashboardStats.map((stat, index) => (
                <Link
                  key={index}
                  to={stat.link}
                  className={`${stat.color} p-6 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:scale-105`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                        {stat.title}
                      </h3>
                      <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">
                        {stat.count}
                      </p>
                    </div>
                    {stat.icon}
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Services/Tasks Section */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              My Services & Tasks
            </h2>
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm">
              {userServices.length > 0 ? (
                <div className="space-y-4">
                  {userServices.map((service) => (
                    <div
                      key={service.id}
                      className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    >
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-800 dark:text-gray-200">
                          {service.title}
                        </h4>
                        {service.description && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {service.description}
                          </p>
                        )}
                        {service.due_date && (
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                            Due: {new Date(service.due_date).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                      <div className="ml-4">
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-medium ${
                            service.status === "COMPLETED"
                              ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                              : service.status === "IN_PROGRESS"
                              ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                              : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                          }`}
                        >
                          {service.status}
                        </span>
                      </div>
                    </div>
                  ))}
                  <div className="text-center pt-4">
                    <Link
                      to="/admin/services"
                      className="text-blue-600 dark:text-blue-400 hover:underline text-sm"
                    >
                      View All Services →
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">
                    No services assigned to you at the moment
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Recent Activity
            </h2>
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm">
              <div className="space-y-4">
                {/* Recent Reminders */}
                {dashboardData?.reminders?.data?.slice(0, 3).map((reminder: any, index: number) => (
                  <div
                    key={`reminder-${index}`}
                    className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Clock className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {reminder.text || "Reminder"}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Due: {reminder.date || "Soon"}
                      </p>
                    </div>
                  </div>
                ))}

                {/* Recent Tickets */}
                {dashboardData?.tickets?.data?.slice(0, 2).map((ticket: any, index: number) => (
                  <div
                    key={`ticket-${index}`}
                    className="flex items-start space-x-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                        <AlertCircle className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Ticket: {ticket.name || "Support Request"}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Status: {ticket.status || "Open"} • {ticket.date || "Recent"}
                      </p>
                    </div>
                  </div>
                ))}

                {/* No Activity Message */}
                {(!dashboardData?.reminders?.data?.length && !dashboardData?.tickets?.data?.length) && (
                  <div className="text-center py-8">
                    <Activity className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      No recent activity to display
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

        </div>

        {/* Add Prospect Modal */}
        {isAddModalOpen && (
          <AddProspects
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
          />
        )}
      </div>
    </Screen>
  );
};
export default Home;